<?php

namespace App\Livewire;

use Livewire\Component;

class CartItems extends Component
{
    public $cart = [];
    public $zipCode = '';
    public $deliveryCost = 0;
    public $subtotal = 0;
    public $tax = 0;
    public $total = 0;

    public function mount()
    {
        $this->cart = session('cart', []);
        $this->deliveryCost = session('delivery_cost', 0);

        // Load previously entered zip code from session
        $this->zipCode = session('zip_code', '');

        $this->calculateTotals();
    }

    public function updated($propertyName)
    {
        // Save zip code to session whenever it's updated
        if ($propertyName === 'zipCode') {
            session(['zip_code' => $this->zipCode]);
            // Also update checkout form session for consistency
            session(['checkout_form.zipCode' => $this->zipCode]);
        }
    }

    public function calculateDelivery()
    {
        if (empty($this->zipCode)) {
            $this->dispatch('show-alert', [
                'message' => 'Please enter your zip code to calculate delivery.',
                'type' => 'warning'
            ]);
            return;
        }

        // Save zip code to session when delivery is calculated
        session([
            'zip_code' => $this->zipCode,
            'checkout_form.zipCode' => $this->zipCode
        ]);

        $this->deliveryCost = round(collect($this->cart)->sum(function ($item) {
            return $item['deliveryCost'] ?? 0;
        }), 2);
        session(['delivery_cost' => $this->deliveryCost]);
        $this->calculateTotals();
        $this->dispatch('delivery-calculated', ['cost' => $this->deliveryCost]);
        $this->dispatch('show-alert', [
            'message' => 'Delivery cost calculated successfully! Total delivery cost: $' . number_format($this->deliveryCost, 2),
            'type' => 'success'
        ]);
    }

    public function updateQuantity($key, $qty)
    {
        if (isset($this->cart[$key])) {
            $this->cart[$key]['qty'] = max(1, (int) $qty);

            // Reassign cart to trigger Livewire reactivity
            $this->cart = [...$this->cart];
            session(['cart' => $this->cart]);

            $this->calculateTotals();
        }
    }

    public function updateRentalMonths($key, $months)
    {
        if (isset($this->cart[$key]) && $this->cart[$key]['type'] === 'Rental') {
            // Ensure months is between 1 and 12
            $months = max(1, min(12, (int) $months));

            // Use base rental price if available, otherwise calculate from current price
            if (isset($this->cart[$key]['baseRentalPrice'])) {
                $baseMonthlyPrice = $this->cart[$key]['baseRentalPrice'];
            } else {
                // Fallback: calculate from current price and months
                $currentMonths = $this->cart[$key]['rentalMonths'] ?? 1;
                $baseMonthlyPrice = $this->cart[$key]['price'] / max(1, $currentMonths);
            }

            // Update rental months and recalculate total price
            $this->cart[$key]['rentalMonths'] = $months;
            $this->cart[$key]['price'] = $baseMonthlyPrice * $months;

            // Update cart key to reflect new months
            $keyParts = explode('-', $key);
            if (count($keyParts) >= 3) {
                $newKey = $keyParts[0] . '-' . $keyParts[1] . '-' . $months;

                // If new key is different, update the cart
                if ($newKey !== $key) {
                    $this->cart[$newKey] = $this->cart[$key];
                    unset($this->cart[$key]);
                }
            }

            // Reassign cart to trigger Livewire reactivity
            $this->cart = [...$this->cart];
            session(['cart' => $this->cart]);

            $this->calculateTotals();
        }
    }

    private function calculateTotals()
    {
        $this->subtotal = collect($this->cart)->sum(function ($item) {
            return $item['price'] * ($item['qty'] ?? 1);
        });

        $this->tax = round(($this->subtotal + $this->deliveryCost) * 0.13, 2);
        $this->total = round($this->subtotal + $this->deliveryCost + $this->tax, 2);

        session([
            'cart_subtotal' => $this->subtotal,
            'cart_tax' => $this->tax,
            'cart_total' => $this->total
        ]);
        
        $this->dispatch('cart-updated', [
            'count' => count($this->cart),
            'subtotal' => $this->subtotal,
            'tax' => $this->tax,
            'total' => $this->total
        ]);

        // $this->reset();
    }

    public function removeItem($key)
    {
        if (isset($this->cart[$key])) {
            unset($this->cart[$key]);
            
            // Reassign cart to trigger Livewire reactivity
            $this->cart = [...$this->cart];
            session(['cart' => $this->cart]);
            
            $this->calculateTotals();

            $this->dispatch('show-alert', [
                'message' => 'Item removed from cart.',
                'type' => 'success'
            ]);
        }
    }

    public function render()
    {
        return view('livewire.cart-items');
    }
}