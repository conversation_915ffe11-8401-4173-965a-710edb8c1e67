<?php $__env->startSection('title', 'Customers'); ?>

<!-- Vendor <PERSON> -->
<?php $__env->startSection('vendor-style'); ?>
<?php echo app('Illuminate\Foundation\Vite')([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss',
  'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss',
  'resources/assets/vendor/libs/select2/select2.scss',
  'resources/assets/vendor/libs/@form-validation/form-validation.scss',
  'resources/assets/vendor/libs/animate-css/animate.scss',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss'
]); ?>
<?php $__env->stopSection(); ?>

<!-- Vendor <PERSON> -->
<?php $__env->startSection('vendor-script'); ?>
<?php echo app('Illuminate\Foundation\Vite')([
  'resources/assets/vendor/libs/moment/moment.js',
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js',
  'resources/assets/vendor/libs/select2/select2.js',
  'resources/assets/vendor/libs/@form-validation/popular.js',
  'resources/assets/vendor/libs/@form-validation/bootstrap5.js',
  'resources/assets/vendor/libs/@form-validation/auto-focus.js',
  'resources/assets/vendor/libs/sweetalert2/sweetalert2.js'
]); ?>
<?php $__env->stopSection(); ?>

<!-- Page Scripts -->
<?php $__env->startSection('page-script'); ?>
<?php echo app('Illuminate\Foundation\Vite')(['resources/js/customers.js']); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<!-- Users List Table -->
<div class="card">
  <div class="card-header border-bottom">
    <h5 class="card-title mb-0">Orders</h5>
  </div>
  <div class="card-datatable table-responsive">
    <table class="datatables-users table">
      <thead class="border-top">
        <tr>
          <th></th>
          <th>Id</th>
          <th>Billing Address</th>
          <th>Billing City</th>
          <th>Billing State</th>
          <th>Billing Zip</th>
          <th>Billing Country</th>
          <th>Shipping Address</th>
          <th>Shipping City</th>
          <th>Shipping State</th>
          <th>Shipping Zip</th>
          <th>Shipping Country</th>
          <th>Container</th>
          <th>Subtotal</th>
          <th>Delivery Cost</th>
          <th>Tax Amount</th>
          <th>Total</th>
          <th>Stripe Transaction ID</th>
          <th>Actions</th>
        </tr>
      </thead>
    </table>
  </div>
  <!-- Offcanvas to add new customer -->
  <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasAddUser" aria-labelledby="offcanvasAddUserLabel">
    <div class="offcanvas-header border-bottom">
      <h5 id="offcanvasAddUserLabel" class="offcanvas-title">Add Order</h5>
      <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body mx-0 flex-grow-0 p-6 h-100">
      <form class="add-new-user pt-0" id="addNewUserForm">
        <input type="hidden" name="id" id="customer_id">
        <div class="mb-6">
          <label class="form-label" for="billing_address">Billing Address</label>
          <input type="text" class="form-control" id="billing_address" name="billing_address" />
        </div>
        <div class="mb-6">
          <label class="form-label" for="billing_city">Billing City</label>
          <input type="text" class="form-control" id="billing_city" name="billing_city" />
        </div>
        <div class="mb-6">
          <label class="form-label" for="billing_state">Billing State</label>
          <input type="text" class="form-control" id="billing_state" name="billing_state" />
        </div>
        <div class="mb-6">
          <label class="form-label" for="billing_zip">Billing Zip</label>
          <input type="text" class="form-control" id="billing_zip" name="billing_zip" />
        </div>
        <div class="mb-6">
          <label class="form-label" for="billing_country">Billing Country</label>
          <input type="text" class="form-control" id="billing_country" name="billing_country" />
        </div>
        <div class="mb-6">
          <label class="form-label" for="shipping_address">Shipping Address</label>
          <input type="text" class="form-control" id="shipping_address" name="shipping_address" />
        </div>
        <div class="mb-6">
          <label class="form-label" for="shipping_city">Shipping City</label>
          <input type="text" class="form-control" id="shipping_city" name="shipping_city" />
        </div>
        <div class="mb-6">
          <label class="form-label" for="shipping_state">Shipping State</label>
          <input type="text" class="form-control" id="shipping_state" name="shipping_state" />
        </div>
        <div class="mb-6">
          <label class="form-label" for="shipping_zip">Shipping Zip</label>
          <input type="text" class="form-control" id="shipping_zip" name="shipping_zip" />
        </div>
        <div class="mb-6">
          <label class="form-label" for="shipping_country">Shipping Country</label>
          <input type="text" class="form-control" id="shipping_country" name="shipping_country" />
        </div>
        <div class="mb-6">
          <label class="form-label" for="container_id">Container</label>
          <select id="container_id" class="form-select" name="container_id"></select>
        </div>
        <div class="mb-6">
          <label class="form-label" for="subtotal">Subtotal</label>
          <input type="number" class="form-control" id="subtotal" name="subtotal" />
        </div>
        <div class="mb-6">
          <label class="form-label" for="delivery_cost">Delivery Cost</label>
          <input type="number" class="form-control" id="delivery_cost" name="delivery_cost" />
        </div>
        <div class="mb-6">
          <label class="form-label" for="tax_amount">Tax Amount</label>
          <input type="number" class="form-control" id="tax_amount" name="tax_amount" />
        </div>
        <div class="mb-6">
          <label class="form-label" for="total">Total</label>
          <input type="number" class="form-control" id="total" name="total" />
        </div>
        <div class="mb-6">
          <label class="form-label" for="stripe_transaction_id">Stripe Transaction ID</label>
          <input type="text" class="form-control" id="stripe_transaction_id" name="stripe_transaction_id" />
        </div>
        <button type="submit" class="btn btn-primary me-3 data-submit">Submit</button>
        <button type="reset" class="btn btn-label-danger" data-bs-dismiss="offcanvas">Cancel</button>
      </form>
    </div>
  </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts/layoutMaster', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\containers\resources\views/dashboard/customers.blade.php ENDPATH**/ ?>